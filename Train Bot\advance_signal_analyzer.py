#!/usr/bin/env python3
"""
Advance Signal Analysis Bot
Analyzes time-based candle behavior over N working days to find repeating patterns
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
import asyncio
from typing import List, Dict, Tuple, Optional
import holidays

# Import existing utilities
from utils import print_colored, print_header, fetch_live_candles, get_oanda_headers
from config import OANDA_CONFIG
import requests


class AdvanceSignalAnalyzer:
    """Advanced signal analysis for finding repeating time-based patterns"""
    
    def __init__(self):
        self.signals_dir = "advance_signals"
        self.ensure_signals_directory()
        
    def ensure_signals_directory(self):
        """Create signals directory if it doesn't exist"""
        if not os.path.exists(self.signals_dir):
            os.makedirs(self.signals_dir)
            print_colored(f"📁 Created directory: {self.signals_dir}", "INFO")
    
    async def run_analysis(self):
        """Main entry point for advance signal analysis"""
        print_header("🔮 ADVANCE SIGNAL ANALYSIS BOT")
        print_colored("🎯 Analyze time-based candle patterns across multiple working days", "INFO", bold=True)
        print_colored("📊 Find repeating time slots with consistent direction and technical confirmation", "INFO")
        print()
        
        try:
            # Get user inputs
            inputs = await self.get_user_inputs()
            if not inputs:
                return
            
            # Calculate working days
            working_days = self.calculate_working_days(inputs['days'])
            print_colored(f"📅 Analyzing {len(working_days)} working days: {working_days[0].strftime('%Y-%m-%d')} to {working_days[-1].strftime('%Y-%m-%d')}", "INFO")
            
            # Fetch and store daily data
            daily_data = await self.fetch_daily_data(inputs, working_days)
            if not daily_data:
                print_colored("❌ Failed to fetch sufficient data for analysis", "ERROR")
                return
            
            # Analyze time slots for patterns
            signals = await self.analyze_time_slots(inputs, daily_data)
            
            # Display results
            self.display_results(inputs, signals)
            
        except KeyboardInterrupt:
            print_colored("\n⚠️ Analysis interrupted by user", "WARNING")
        except Exception as e:
            print_colored(f"❌ Error during analysis: {e}", "ERROR")
        
        print()
        input("Press Enter to return to main menu...")
    
    async def get_user_inputs(self) -> Optional[Dict]:
        """Get and validate user inputs"""
        print_colored("📝 Please provide the following information:", "SKY_BLUE", bold=True)
        print()
        
        try:
            # Trading pair
            pair = input("1. Enter trading pair (e.g., EUR/USD, BTC/USD): ").strip().upper()
            if not pair:
                print_colored("❌ Trading pair is required", "ERROR")
                return None
            
            # Convert to OANDA format
            oanda_pair = self.convert_to_oanda_format(pair)
            if not oanda_pair:
                print_colored(f"❌ Unsupported trading pair: {pair}", "ERROR")
                return None
            
            # Timeframe
            print("\nAvailable timeframes:")
            timeframes = {"1": "M1", "5": "M5", "15": "M15", "30": "M30", "60": "H1"}
            for key, value in timeframes.items():
                print_colored(f"  {key} = {value} ({key} minute{'s' if key != '1' else ''})", "INFO")
            
            tf_choice = input("\n2. Enter timeframe (1, 5, 15, 30, 60): ").strip()
            if tf_choice not in timeframes:
                print_colored("❌ Invalid timeframe choice", "ERROR")
                return None
            
            timeframe = timeframes[tf_choice]
            
            # Number of days
            try:
                days = int(input("3. Enter number of past working days to analyze (3-10): ").strip())
                if days < 3 or days > 10:
                    print_colored("❌ Number of days must be between 3 and 10", "ERROR")
                    return None
            except ValueError:
                print_colored("❌ Invalid number of days", "ERROR")
                return None
            
            # Time range
            print("\n4. Enter time range for analysis (24-hour format):")
            start_time = input("   Start time (e.g., 12:35): ").strip()
            end_time = input("   End time (e.g., 16:35): ").strip()
            
            if not self.validate_time_format(start_time) or not self.validate_time_format(end_time):
                print_colored("❌ Invalid time format. Use HH:MM format", "ERROR")
                return None
            
            return {
                'pair': pair,
                'oanda_pair': oanda_pair,
                'timeframe': timeframe,
                'timeframe_minutes': int(tf_choice),
                'days': days,
                'start_time': start_time,
                'end_time': end_time
            }
            
        except KeyboardInterrupt:
            return None
    
    def convert_to_oanda_format(self, pair: str) -> Optional[str]:
        """Convert trading pair to OANDA format"""
        # Remove common separators
        pair = pair.replace("/", "").replace("-", "").replace("_", "")
        
        # Common pair mappings
        mappings = {
            "EURUSD": "EUR_USD",
            "GBPUSD": "GBP_USD", 
            "USDJPY": "USD_JPY",
            "AUDUSD": "AUD_USD",
            "USDCAD": "USD_CAD",
            "USDCHF": "USD_CHF",
            "NZDUSD": "NZD_USD",
            "EURGBP": "EUR_GBP",
            "EURJPY": "EUR_JPY",
            "GBPJPY": "GBP_JPY",
            "BTCUSD": "BTC_USD",
            "ETHUSD": "ETH_USD"
        }
        
        return mappings.get(pair)
    
    def validate_time_format(self, time_str: str) -> bool:
        """Validate time format HH:MM"""
        try:
            time.fromisoformat(time_str)
            return True
        except ValueError:
            return False
    
    def calculate_working_days(self, num_days: int) -> List[datetime]:
        """Calculate working days going back from today"""
        working_days = []
        current_date = datetime.now().date()
        
        # Get holidays for current year
        us_holidays = holidays.US(years=current_date.year)
        
        days_found = 0
        days_back = 0
        
        while days_found < num_days:
            check_date = current_date - timedelta(days=days_back)
            
            # Skip weekends and holidays
            if check_date.weekday() < 5 and check_date not in us_holidays:
                working_days.append(datetime.combine(check_date, datetime.min.time()))
                days_found += 1
            
            days_back += 1
            
            # Safety check to prevent infinite loop
            if days_back > 30:
                break
        
        return list(reversed(working_days))  # Return in chronological order
    
    async def fetch_daily_data(self, inputs: Dict, working_days: List[datetime]) -> Dict[str, pd.DataFrame]:
        """Fetch and store data for each working day"""
        print_colored("📊 Fetching historical data for analysis...", "INFO", bold=True)
        
        daily_data = {}
        successful_days = 0
        
        for i, day in enumerate(working_days, 1):
            day_key = f"day{i}"
            print_colored(f"📅 Fetching data for {day.strftime('%Y-%m-%d')} ({day_key})...", "INFO")
            
            try:
                # Fetch data for the specific day
                df = await self.fetch_day_data(inputs, day)
                
                if df is not None and len(df) > 0:
                    # Save to CSV
                    csv_path = os.path.join(self.signals_dir, f"{day_key}.csv")
                    df.to_csv(csv_path, index=False)
                    
                    daily_data[day_key] = df
                    successful_days += 1
                    print_colored(f"✅ {day_key}: {len(df)} candles saved", "SUCCESS")
                else:
                    print_colored(f"❌ {day_key}: No data available", "ERROR")
                    
            except Exception as e:
                print_colored(f"❌ {day_key}: Error fetching data - {e}", "ERROR")
        
        print_colored(f"📊 Successfully fetched data for {successful_days}/{len(working_days)} days", "INFO")
        
        if successful_days < inputs['days'] * 0.7:  # Need at least 70% of days
            print_colored("⚠️ Insufficient data for reliable analysis", "WARNING")
            return None
        
        return daily_data
    
    async def fetch_day_data(self, inputs: Dict, day: datetime) -> Optional[pd.DataFrame]:
        """Fetch data for a specific day and time range"""
        try:
            # For historical data, we need to fetch enough candles to cover the time range
            # plus extra for technical indicators
            
            # Calculate how many candles we need for the time range
            start_time = datetime.strptime(inputs['start_time'], '%H:%M').time()
            end_time = datetime.strptime(inputs['end_time'], '%H:%M').time()
            
            # Calculate minutes in range
            start_minutes = start_time.hour * 60 + start_time.minute
            end_minutes = end_time.hour * 60 + end_time.minute
            
            if end_minutes <= start_minutes:
                end_minutes += 24 * 60  # Next day
            
            range_minutes = end_minutes - start_minutes
            candles_needed = range_minutes // inputs['timeframe_minutes']
            
            # Add extra candles for technical indicators (at least 50 for EMA calculations)
            total_candles = max(candles_needed + 100, 200)
            
            # Fetch data using OANDA API
            df = fetch_live_candles(inputs['oanda_pair'], count=total_candles, granularity=inputs['timeframe'])
            
            if df is not None and len(df) > 0:
                # Filter to the specific day and time range
                df = self.filter_to_day_and_time_range(df, day, inputs['start_time'], inputs['end_time'])
                return df
            
            return None
            
        except Exception as e:
            print_colored(f"Error fetching day data: {e}", "ERROR")
            return None

    def filter_to_day_and_time_range(self, df: pd.DataFrame, target_day: datetime, start_time: str, end_time: str) -> pd.DataFrame:
        """Filter dataframe to specific day and time range"""
        try:
            # Ensure datetime column exists and is properly formatted
            if 'datetime' not in df.columns:
                return df

            # Convert to datetime if needed
            df['datetime'] = pd.to_datetime(df['datetime'])

            # Filter to target day
            target_date = target_day.date()
            df = df[df['datetime'].dt.date == target_date].copy()

            if len(df) == 0:
                return df

            # Filter to time range
            start_time_obj = datetime.strptime(start_time, '%H:%M').time()
            end_time_obj = datetime.strptime(end_time, '%H:%M').time()

            df = df[
                (df['datetime'].dt.time >= start_time_obj) &
                (df['datetime'].dt.time <= end_time_obj)
            ].copy()

            return df

        except Exception as e:
            print_colored(f"Error filtering data: {e}", "ERROR")
            return df

    async def analyze_time_slots(self, inputs: Dict, daily_data: Dict[str, pd.DataFrame]) -> List[Dict]:
        """Analyze each time slot for consistent patterns across all days"""
        print_colored("🔍 Analyzing time slots for consistent patterns...", "INFO", bold=True)

        signals = []

        # Generate all time slots in the range
        time_slots = self.generate_time_slots(inputs['start_time'], inputs['end_time'], inputs['timeframe_minutes'])

        print_colored(f"⏰ Checking {len(time_slots)} time slots across {len(daily_data)} days", "INFO")

        for time_slot in time_slots:
            try:
                # Get candles for this time slot from all days
                slot_candles = self.get_candles_for_time_slot(daily_data, time_slot)

                if len(slot_candles) < len(daily_data) * 0.8:  # Need at least 80% of days
                    continue

                # Check if all candles have same direction
                direction = self.check_same_direction(slot_candles)
                if not direction:
                    continue

                # Check candle strength
                if not self.check_candle_strength(slot_candles):
                    continue

                # Apply technical filters
                if await self.apply_technical_filters(slot_candles, direction, daily_data, time_slot):
                    signals.append({
                        'time': time_slot,
                        'direction': direction,
                        'candle_count': len(slot_candles),
                        'confidence': self.calculate_confidence(slot_candles, direction)
                    })

            except Exception as e:
                print_colored(f"Error analyzing time slot {time_slot}: {e}", "ERROR")
                continue

        print_colored(f"✅ Found {len(signals)} qualifying signals", "SUCCESS")
        return signals

    def generate_time_slots(self, start_time: str, end_time: str, interval_minutes: int) -> List[str]:
        """Generate all time slots in the specified range"""
        slots = []

        start_dt = datetime.strptime(start_time, '%H:%M')
        end_dt = datetime.strptime(end_time, '%H:%M')

        # Handle overnight ranges
        if end_dt <= start_dt:
            end_dt += timedelta(days=1)

        current = start_dt
        while current <= end_dt:
            slots.append(current.strftime('%H:%M'))
            current += timedelta(minutes=interval_minutes)

        return slots

    def get_candles_for_time_slot(self, daily_data: Dict[str, pd.DataFrame], time_slot: str) -> List[Dict]:
        """Get candles for a specific time slot from all days"""
        candles = []

        for day_key, df in daily_data.items():
            if 'datetime' not in df.columns:
                continue

            # Find candle for this time slot
            target_time = datetime.strptime(time_slot, '%H:%M').time()

            # Filter to exact time or closest time
            df['time'] = pd.to_datetime(df['datetime']).dt.time
            matching_candles = df[df['time'] == target_time]

            if len(matching_candles) > 0:
                candle = matching_candles.iloc[0]
                candles.append({
                    'day': day_key,
                    'open': float(candle['open']),
                    'high': float(candle['high']),
                    'low': float(candle['low']),
                    'close': float(candle['close']),
                    'volume': float(candle.get('volume', 0)),
                    'datetime': candle['datetime']
                })

        return candles

    def check_same_direction(self, candles: List[Dict]) -> Optional[str]:
        """Check if all candles have the same direction"""
        if not candles:
            return None

        directions = []
        for candle in candles:
            if candle['close'] > candle['open']:
                directions.append('UP')
            elif candle['close'] < candle['open']:
                directions.append('DOWN')
            else:
                directions.append('DOJI')  # No direction

        # Check if all directions are the same (and not DOJI)
        unique_directions = set(directions)
        if len(unique_directions) == 1 and 'DOJI' not in unique_directions:
            return directions[0]

        return None

    def check_candle_strength(self, candles: List[Dict]) -> bool:
        """Check if all candles meet strength requirements"""
        for candle in candles:
            body_size = abs(candle['close'] - candle['open'])
            total_range = candle['high'] - candle['low']

            if total_range == 0:
                return False

            body_percentage = body_size / total_range

            # Body must be > 60% of total range
            if body_percentage < 0.6:
                return False

            # Check wick requirements (< 40% each)
            upper_wick = candle['high'] - max(candle['open'], candle['close'])
            lower_wick = min(candle['open'], candle['close']) - candle['low']

            upper_wick_pct = upper_wick / total_range if total_range > 0 else 0
            lower_wick_pct = lower_wick / total_range if total_range > 0 else 0

            if upper_wick_pct > 0.4 or lower_wick_pct > 0.4:
                return False

        return True

    async def apply_technical_filters(self, candles: List[Dict], direction: str, daily_data: Dict[str, pd.DataFrame], time_slot: str) -> bool:
        """Apply RSI, EMA trend, and MA confirmation filters"""
        try:
            # For each candle, check technical conditions
            for candle in candles:
                day_key = candle['day']
                if day_key not in daily_data:
                    continue

                df = daily_data[day_key]
                candle_datetime = candle['datetime']

                # Get data up to this candle for indicator calculation
                historical_data = df[df['datetime'] <= candle_datetime].copy()

                if len(historical_data) < 50:  # Need enough data for indicators
                    return False

                # Calculate technical indicators
                rsi = self.calculate_rsi(historical_data['close'], period=14)
                ema20 = self.calculate_ema(historical_data['close'], period=20)
                ema50 = self.calculate_ema(historical_data['close'], period=50)

                if len(rsi) == 0 or len(ema20) == 0 or len(ema50) == 0:
                    return False

                current_rsi = rsi.iloc[-1]
                current_ema20 = ema20.iloc[-1]
                current_ema50 = ema50.iloc[-1]
                current_close = candle['close']

                # Apply RSI filter
                if direction == 'DOWN' and current_rsi >= 40:
                    return False
                elif direction == 'UP' and current_rsi <= 60:
                    return False

                # Apply trend filter (EMA20 vs EMA50)
                if direction == 'DOWN' and current_ema20 >= current_ema50:
                    return False
                elif direction == 'UP' and current_ema20 <= current_ema50:
                    return False

                # Apply MA confirmation (close vs EMA20)
                if direction == 'DOWN' and current_close >= current_ema20:
                    return False
                elif direction == 'UP' and current_close <= current_ema20:
                    return False

            return True

        except Exception as e:
            print_colored(f"Error in technical filters: {e}", "ERROR")
            return False

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))

            return rsi.fillna(50)  # Fill NaN with neutral value

        except Exception:
            return pd.Series([50] * len(prices), index=prices.index)

    def calculate_ema(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate Exponential Moving Average"""
        try:
            return prices.ewm(span=period, adjust=False).mean()
        except Exception:
            return pd.Series([prices.mean()] * len(prices), index=prices.index)

    def calculate_confidence(self, candles: List[Dict], direction: str) -> float:
        """Calculate confidence score for the signal"""
        try:
            total_score = 0
            max_score = 0

            for candle in candles:
                # Base score for consistent direction
                total_score += 60
                max_score += 100

                # Bonus for strong candle body
                body_size = abs(candle['close'] - candle['open'])
                total_range = candle['high'] - candle['low']

                if total_range > 0:
                    body_strength = body_size / total_range
                    if body_strength > 0.7:
                        total_score += 15
                    elif body_strength > 0.6:
                        total_score += 10

                # Bonus for volume (if available)
                if candle.get('volume', 0) > 0:
                    total_score += 5

            confidence = (total_score / max_score) * 100 if max_score > 0 else 0
            return min(confidence, 100)

        except Exception:
            return 50.0

    def display_results(self, inputs: Dict, signals: List[Dict]):
        """Display the final signal results"""
        print_header("📅 ADVANCE SIGNAL ANALYSIS RESULTS")

        print_colored(f"📊 Analysis Summary:", "SKY_BLUE", bold=True)
        print_colored(f"   Trading Pair: {inputs['pair']} ({inputs['oanda_pair']})", "INFO")
        print_colored(f"   Timeframe: {inputs['timeframe']} ({inputs['timeframe_minutes']} minutes)", "INFO")
        print_colored(f"   Analysis Period: {inputs['days']} working days", "INFO")
        print_colored(f"   Time Range: {inputs['start_time']} to {inputs['end_time']}", "INFO")
        print()

        if not signals:
            print_colored("❌ No qualifying signals found", "ERROR", bold=True)
            print_colored("💡 Try adjusting your criteria:", "INFO")
            print_colored("   • Increase the number of analysis days", "INFO")
            print_colored("   • Expand the time range", "INFO")
            print_colored("   • Choose a different timeframe", "INFO")
            return

        print_colored(f"✅ Found {len(signals)} qualifying signals:", "SUCCESS", bold=True)
        print()

        # Sort signals by time
        signals.sort(key=lambda x: x['time'])

        # Display signals
        for signal in signals:
            direction_color = "SUCCESS" if signal['direction'] == 'UP' else "ERROR"
            direction_emoji = "📈" if signal['direction'] == 'UP' else "📉"

            print_colored(f"{signal['time']} {direction_emoji} {signal['direction']}", direction_color, bold=True)

        print()
        print_colored("📋 Signal Details:", "SKY_BLUE", bold=True)
        for signal in signals:
            direction_color = "SUCCESS" if signal['direction'] == 'UP' else "ERROR"
            print_colored(f"   {signal['time']} {signal['direction']} - Confidence: {signal['confidence']:.1f}% - Days: {signal['candle_count']}", direction_color)

        # Save results to file
        self.save_results_to_file(inputs, signals)

    def save_results_to_file(self, inputs: Dict, signals: List[Dict]):
        """Save results to CSV and text files"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Save as CSV
            if signals:
                df_signals = pd.DataFrame(signals)
                csv_path = os.path.join(self.signals_dir, f"signals_{timestamp}.csv")
                df_signals.to_csv(csv_path, index=False)
                print_colored(f"💾 Signals saved to: {csv_path}", "SUCCESS")

            # Save as text file
            txt_path = os.path.join(self.signals_dir, f"signals_{timestamp}.txt")
            with open(txt_path, 'w') as f:
                f.write(f"Advance Signal Analysis Results\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(f"Trading Pair: {inputs['pair']} ({inputs['oanda_pair']})\n")
                f.write(f"Timeframe: {inputs['timeframe']} ({inputs['timeframe_minutes']} minutes)\n")
                f.write(f"Analysis Period: {inputs['days']} working days\n")
                f.write(f"Time Range: {inputs['start_time']} to {inputs['end_time']}\n\n")

                if signals:
                    f.write("Qualifying Signals:\n")
                    for signal in signals:
                        f.write(f"{signal['time']} {signal['direction']}\n")
                else:
                    f.write("No qualifying signals found.\n")

            print_colored(f"📄 Report saved to: {txt_path}", "SUCCESS")

        except Exception as e:
            print_colored(f"❌ Error saving results: {e}", "ERROR")


# Global instance
advance_signal_analyzer = AdvanceSignalAnalyzer()

# Main function for integration
async def run_advance_signal_analysis():
    """Main entry point for advance signal analysis"""
    await advance_signal_analyzer.run_analysis()
