#!/usr/bin/env python3
"""
Advance Signal Analysis Bot
Analyzes time-based candle behavior over N working days to find repeating patterns
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
import asyncio
from typing import List, Dict, Tuple, Optional
import holidays

# Import existing utilities
from utils import print_colored, print_header, fetch_live_candles, get_oanda_headers
from config import OANDA_CONFIG
import requests


class AdvanceSignalAnalyzer:
    """Advanced signal analysis for finding repeating time-based patterns"""
    
    def __init__(self):
        self.signals_dir = "advance_signals"
        self.ensure_signals_directory()
        
    def ensure_signals_directory(self):
        """Create signals directory if it doesn't exist"""
        if not os.path.exists(self.signals_dir):
            os.makedirs(self.signals_dir)
            print_colored(f"📁 Created directory: {self.signals_dir}", "INFO")
    
    async def run_analysis(self):
        """Main entry point for advance signal analysis"""
        print_header("🔮 ADVANCE SIGNAL ANALYSIS")

        try:
            # Get user inputs
            inputs = await self.get_user_inputs()
            if not inputs:
                return

            # Calculate working days
            working_days = self.calculate_working_days(inputs['days'])

            # Fetch and store daily data
            daily_data = await self.fetch_daily_data(inputs, working_days)
            if not daily_data:
                print_colored("❌ Failed to fetch sufficient data for analysis", "ERROR")
                return

            # Analyze time slots for patterns
            signals, partial_matches = await self.analyze_time_slots(inputs, daily_data)

            # Display results
            self.display_results(inputs, signals, partial_matches)

        except KeyboardInterrupt:
            print_colored("\n⚠️ Analysis interrupted by user", "WARNING")
        except Exception as e:
            print_colored(f"❌ Error during analysis: {e}", "ERROR")

        print()
        input("Press Enter to return to main menu...")
    
    async def get_user_inputs(self) -> Optional[Dict]:
        """Get and validate user inputs"""
        try:
            # Import the same live pairs used in practice/demo mode
            from Model import LIVE_PAIRS_ONLY

            # Trading pair selection - same as practice/demo mode
            print_colored("🌍 Live Currency Pairs (Real OANDA Data):", "SKY_BLUE", bold=True)
            print_colored("=" * 80, "SKY_BLUE")

            # Display pairs in columns like practice mode
            columns = 4
            for i in range(0, len(LIVE_PAIRS_ONLY), columns):
                row = LIVE_PAIRS_ONLY[i:i+columns]
                formatted_row = ""
                for j, pair in enumerate(row):
                    formatted_row += f"{i+j+1:2d}. {pair:<18}"
                print_colored(formatted_row, "ROSEWOOD")

            print_colored("=" * 80, "SKY_BLUE")

            try:
                print_colored("1. Select trading pair (1-{0}): ".format(len(LIVE_PAIRS_ONLY)), "BURNT_ORANGE", bold=True, end="")
                pair_choice = int(input().strip())
                if pair_choice < 1 or pair_choice > len(LIVE_PAIRS_ONLY):
                    print_colored("❌ Invalid pair selection", "ERROR")
                    return None

                selected_pair = LIVE_PAIRS_ONLY[pair_choice - 1]
                # Convert to OANDA format
                oanda_pair = self.convert_to_oanda_format(selected_pair)
                if not oanda_pair:
                    print_colored(f"❌ Cannot convert {selected_pair} to OANDA format", "ERROR")
                    return None

                # Format for display
                pair = f"{selected_pair[:3]}/{selected_pair[3:]}"  # EURUSD -> EUR/USD

            except ValueError:
                print_colored("❌ Please enter a valid number", "ERROR")
                return None

            # Timeframe - professional display with proper colors
            print()
            print_colored("⏰ Available Timeframes:", "SKY_BLUE", bold=True)
            print_colored("=" * 50, "SKY_BLUE")

            timeframes = {
                "1": "M1",
                "2": "M5",
                "3": "M15",
                "4": "M30",
                "5": "H1",
                "6": "H4",
                "7": "D"
            }

            timeframe_mapping = {
                "1": ("M1", 1),
                "2": ("M5", 5),
                "3": ("M15", 15),
                "4": ("M30", 30),
                "5": ("H1", 60),
                "6": ("H4", 240),
                "7": ("D", 1440)
            }

            # Display in columns like pairs
            timeframe_items = list(timeframes.items())
            columns = 2
            for i in range(0, len(timeframe_items), columns):
                row = timeframe_items[i:i+columns]
                formatted_row = ""
                for j, (key, value) in enumerate(row):
                    formatted_row += f"{key}. {value:<15}"
                print_colored(formatted_row, "ROSEWOOD")

            print_colored("=" * 50, "SKY_BLUE")

            print_colored("2. Enter timeframe (1-7): ", "BURNT_ORANGE", bold=True, end="")
            tf_choice = input().strip()
            if tf_choice not in timeframes:
                print_colored("❌ Invalid timeframe choice", "ERROR")
                return None

            timeframe, timeframe_minutes = timeframe_mapping[tf_choice]
            
            # Number of days with professional display
            print()
            print_colored("📅 Analysis Period:", "SKY_BLUE", bold=True)
            print_colored("=" * 30, "SKY_BLUE")
            print_colored("Range: 3-10 working days", "ROSEWOOD")
            print_colored("=" * 30, "SKY_BLUE")

            try:
                print_colored("3. Enter number of past working days to analyze (3-10): ", "BURNT_ORANGE", bold=True, end="")
                days = int(input().strip())
                if days < 3 or days > 10:
                    print_colored("❌ Number of days must be between 3 and 10", "ERROR")
                    return None
            except ValueError:
                print_colored("❌ Invalid number of days", "ERROR")
                return None

            # Time range with professional display
            print()
            print_colored("🕐 Time Range for Analysis:", "SKY_BLUE", bold=True)
            print_colored("=" * 40, "SKY_BLUE")
            print_colored("Format: 24-hour (HH:MM)", "ROSEWOOD")
            print_colored("Example: 12:35 to 16:35", "ROSEWOOD")
            print_colored("=" * 40, "SKY_BLUE")

            print_colored("4. Start time (e.g., 12:35): ", "BURNT_ORANGE", bold=True, end="")
            start_time = input().strip()
            print_colored("   End time (e.g., 16:35): ", "BURNT_ORANGE", bold=True, end="")
            end_time = input().strip()

            if not self.validate_time_format(start_time) or not self.validate_time_format(end_time):
                print_colored("❌ Invalid time format. Use HH:MM format", "ERROR")
                return None

            return {
                'pair': pair,
                'oanda_pair': oanda_pair,
                'timeframe': timeframe,
                'timeframe_minutes': timeframe_minutes,
                'days': days,
                'start_time': start_time,
                'end_time': end_time
            }
            
        except KeyboardInterrupt:
            return None
    
    def convert_to_oanda_format(self, pair: str) -> Optional[str]:
        """Convert trading pair to OANDA format"""
        # Remove common separators
        pair = pair.replace("/", "").replace("-", "").replace("_", "")
        
        # Common pair mappings
        mappings = {
            "EURUSD": "EUR_USD",
            "GBPUSD": "GBP_USD", 
            "USDJPY": "USD_JPY",
            "AUDUSD": "AUD_USD",
            "USDCAD": "USD_CAD",
            "USDCHF": "USD_CHF",
            "NZDUSD": "NZD_USD",
            "EURGBP": "EUR_GBP",
            "EURJPY": "EUR_JPY",
            "GBPJPY": "GBP_JPY",
            "BTCUSD": "BTC_USD",
            "ETHUSD": "ETH_USD"
        }
        
        return mappings.get(pair)
    
    def validate_time_format(self, time_str: str) -> bool:
        """Validate time format HH:MM"""
        try:
            time.fromisoformat(time_str)
            return True
        except ValueError:
            return False
    
    def calculate_working_days(self, num_days: int) -> List[datetime]:
        """Calculate working days going back from yesterday (exclude today)"""
        working_days = []
        # Start from yesterday to avoid "future date" API errors
        current_date = (datetime.now() - timedelta(days=1)).date()

        # Get holidays for current year
        us_holidays = holidays.US(years=current_date.year)

        days_found = 0
        days_back = 0

        while days_found < num_days:
            check_date = current_date - timedelta(days=days_back)

            # Skip weekends and holidays
            if check_date.weekday() < 5 and check_date not in us_holidays:
                working_days.append(datetime.combine(check_date, datetime.min.time()))
                days_found += 1

            days_back += 1

            # Safety check to prevent infinite loop
            if days_back > 50:  # Increased safety limit
                break

        return list(reversed(working_days))  # Return in chronological order
    
    async def fetch_daily_data(self, inputs: Dict, working_days: List[datetime]) -> Dict[str, pd.DataFrame]:
        """Fetch and store data for each working day"""
        daily_data = {}
        successful_days = 0

        for i, day in enumerate(working_days):
            day_date_str = day.strftime('%Y-%m-%d')

            try:
                # Fetch data for the specific day
                df = await self.fetch_day_data(inputs, day)

                if df is not None and len(df) > 0:
                    # Save to CSV with actual date
                    csv_filename = f"{day_date_str}.csv"
                    csv_path = os.path.join(self.signals_dir, csv_filename)
                    df.to_csv(csv_path, index=False)

                    daily_data[day_date_str] = df
                    successful_days += 1
                    print_colored(f"✅ {day_date_str}: {len(df)} candles saved to {csv_filename}", "SUCCESS")
                else:
                    print_colored(f"❌ {day_date_str}: No data available", "ERROR")

            except Exception as e:
                print_colored(f"❌ {day_date_str}: Error fetching data - {e}", "ERROR")

        if successful_days < inputs['days'] * 0.7:  # Need at least 70% of days
            print_colored("⚠️ Insufficient data for reliable analysis", "WARNING")
            return None

        return daily_data
    
    async def fetch_day_data(self, inputs: Dict, day: datetime) -> Optional[pd.DataFrame]:
        """Fetch data for a specific historical day"""
        try:
            from utils import get_oanda_headers
            from config import OANDA_CONFIG
            import requests

            # Calculate the exact start and end times for the target day
            target_date = day.date()
            start_datetime = datetime.combine(target_date, datetime.strptime(inputs['start_time'], '%H:%M').time())
            end_datetime = datetime.combine(target_date, datetime.strptime(inputs['end_time'], '%H:%M').time())

            # Add buffer time for technical indicators (2 hours before start)
            buffer_start = start_datetime - timedelta(hours=2)

            # Convert to RFC3339 format for OANDA API
            from_time = buffer_start.strftime('%Y-%m-%dT%H:%M:%S.000000000Z')
            to_time = end_datetime.strftime('%Y-%m-%dT%H:%M:%S.000000000Z')

            # Fetch historical data using OANDA API with specific date range
            headers = get_oanda_headers()
            url = f"{OANDA_CONFIG['BASE_URL']}/v3/instruments/{inputs['oanda_pair']}/candles"

            params = {
                'from': from_time,
                'to': to_time,
                'granularity': inputs['timeframe'],
                'price': 'M'  # Mid price
            }

            response = requests.get(url, headers=headers, params=params, timeout=30)

            if response.status_code == 200:
                data = response.json()
                candles = data.get('candles', [])

                if candles:
                    # Process candle data
                    df = self.process_historical_candles(candles)

                    if df is not None and len(df) > 0:
                        # Filter to exact time range
                        df = self.filter_to_day_and_time_range(df, day, inputs['start_time'], inputs['end_time'])
                        return df
                else:
                    print_colored(f"   No candles returned for {target_date}", "WARNING")
            else:
                print_colored(f"   API Error: {response.status_code} - {response.text}", "ERROR")

            return None

        except Exception as e:
            print_colored(f"Error fetching day data: {e}", "ERROR")
            return None

    def process_historical_candles(self, candles: List[Dict]) -> Optional[pd.DataFrame]:
        """Process historical candle data from OANDA API"""
        try:
            processed_data = []

            for candle in candles:
                if candle.get('complete', True):  # Only use complete candles
                    mid = candle.get('mid', {})
                    processed_data.append({
                        'datetime': candle['time'],
                        'open': float(mid.get('o', 0)),
                        'high': float(mid.get('h', 0)),
                        'low': float(mid.get('l', 0)),
                        'close': float(mid.get('c', 0)),
                        'volume': int(candle.get('volume', 0))
                    })

            if processed_data:
                df = pd.DataFrame(processed_data)
                df['datetime'] = pd.to_datetime(df['datetime'])
                return df

            return None

        except Exception as e:
            print_colored(f"Error processing candles: {e}", "ERROR")
            return None

    def filter_to_day_and_time_range(self, df: pd.DataFrame, target_day: datetime, start_time: str, end_time: str) -> pd.DataFrame:
        """Filter dataframe to specific day and time range"""
        try:
            # Ensure datetime column exists and is properly formatted
            if 'datetime' not in df.columns:
                return df

            # Convert to datetime if needed
            df['datetime'] = pd.to_datetime(df['datetime'])

            # Filter to target day
            target_date = target_day.date()
            df = df[df['datetime'].dt.date == target_date].copy()

            if len(df) == 0:
                return df

            # Filter to time range
            start_time_obj = datetime.strptime(start_time, '%H:%M').time()
            end_time_obj = datetime.strptime(end_time, '%H:%M').time()

            df = df[
                (df['datetime'].dt.time >= start_time_obj) &
                (df['datetime'].dt.time <= end_time_obj)
            ].copy()

            return df

        except Exception as e:
            print_colored(f"Error filtering data: {e}", "ERROR")
            return df

    async def analyze_time_slots(self, inputs: Dict, daily_data: Dict[str, pd.DataFrame]) -> Tuple[List[Dict], List[Dict]]:
        """Analyze each time slot for consistent patterns across all days"""
        print_colored("🔍 Analyzing time slots for consistent patterns...", "INFO", bold=True)

        signals = []
        partial_matches = []

        # Generate all time slots in the range
        time_slots = self.generate_time_slots(inputs['start_time'], inputs['end_time'], inputs['timeframe_minutes'])

        print_colored(f"⏰ Checking {len(time_slots)} time slots across {len(daily_data)} days", "INFO")

        for time_slot in time_slots:
            try:
                # Get candles for this time slot from all days
                slot_candles = self.get_candles_for_time_slot(daily_data, time_slot)

                if len(slot_candles) < len(daily_data) * 0.5:  # Need at least 50% of days (relaxed)
                    continue

                # Check if all candles have same direction
                direction = self.check_same_direction(slot_candles)
                if not direction:
                    continue

                # Check candle strength with detailed results
                strength_passed, strength_details = self.check_candle_strength(slot_candles)

                # Apply technical filters with detailed results
                filters_passed, filter_details = await self.apply_technical_filters(slot_candles, direction, daily_data, time_slot)

                # Create signal data
                signal_data = {
                    'time': time_slot,
                    'direction': direction,
                    'candle_count': len(slot_candles),
                    'confidence': self.calculate_confidence(slot_candles, direction),
                    'strength_passed': strength_passed,
                    'strength_details': strength_details,
                    'filters_passed': filters_passed,
                    'filter_details': filter_details
                }

                if strength_passed and filters_passed:
                    # Full qualifying signal
                    signals.append(signal_data)
                elif direction:  # At least has consistent direction
                    # Partial match - show what failed
                    signal_data['failed_reasons'] = []
                    if not strength_passed:
                        signal_data['failed_reasons'].append('candle_strength')
                    if not filters_passed:
                        signal_data['failed_reasons'].append('technical_filters')
                    partial_matches.append(signal_data)

            except Exception as e:
                print_colored(f"Error analyzing time slot {time_slot}: {e}", "ERROR")
                continue

        print_colored(f"✅ Found {len(signals)} qualifying signals and {len(partial_matches)} partial matches", "SUCCESS")
        return signals, partial_matches

    def generate_time_slots(self, start_time: str, end_time: str, interval_minutes: int) -> List[str]:
        """Generate all time slots in the specified range"""
        slots = []

        start_dt = datetime.strptime(start_time, '%H:%M')
        end_dt = datetime.strptime(end_time, '%H:%M')

        # Handle overnight ranges
        if end_dt <= start_dt:
            end_dt += timedelta(days=1)

        current = start_dt
        while current <= end_dt:
            slots.append(current.strftime('%H:%M'))
            current += timedelta(minutes=interval_minutes)

        return slots

    def get_candles_for_time_slot(self, daily_data: Dict[str, pd.DataFrame], time_slot: str) -> List[Dict]:
        """Get candles for a specific time slot from all days"""
        candles = []

        for day_key, df in daily_data.items():
            if 'datetime' not in df.columns:
                continue

            # Find candle for this time slot
            target_time = datetime.strptime(time_slot, '%H:%M').time()

            # Filter to exact time or closest time
            df_copy = df.copy()
            df_copy['time'] = pd.to_datetime(df_copy['datetime']).dt.time
            matching_candles = df_copy[df_copy['time'] == target_time]

            if len(matching_candles) > 0:
                candle = matching_candles.iloc[0]
                candles.append({
                    'day': day_key,  # This will now be the date string like '2025-08-01'
                    'open': float(candle['open']),
                    'high': float(candle['high']),
                    'low': float(candle['low']),
                    'close': float(candle['close']),
                    'volume': float(candle.get('volume', 0)),
                    'datetime': candle['datetime']
                })
            else:
                # Try to find closest time within 1 minute
                df_copy['time_diff'] = df_copy['time'].apply(
                    lambda x: abs((datetime.combine(datetime.today(), x) -
                                 datetime.combine(datetime.today(), target_time)).total_seconds())
                )
                closest_candles = df_copy[df_copy['time_diff'] <= 60]  # Within 1 minute

                if len(closest_candles) > 0:
                    candle = closest_candles.loc[closest_candles['time_diff'].idxmin()]
                    candles.append({
                        'day': day_key,
                        'open': float(candle['open']),
                        'high': float(candle['high']),
                        'low': float(candle['low']),
                        'close': float(candle['close']),
                        'volume': float(candle.get('volume', 0)),
                        'datetime': candle['datetime']
                    })

        return candles

    def check_same_direction(self, candles: List[Dict]) -> Optional[str]:
        """Check if all candles have the same direction"""
        if not candles:
            return None

        directions = []
        for candle in candles:
            if candle['close'] > candle['open']:
                directions.append('UP')
            elif candle['close'] < candle['open']:
                directions.append('DOWN')
            else:
                directions.append('DOJI')  # No direction

        # Check if all directions are the same (and not DOJI)
        unique_directions = set(directions)
        if len(unique_directions) == 1 and 'DOJI' not in unique_directions:
            return directions[0]

        return None

    def check_candle_strength(self, candles: List[Dict]) -> Tuple[bool, List[Dict]]:
        """Check candle strength - at least 3 out of 4 candles must have body > 40%"""
        strong_candles = []
        weak_candles = []

        for i, candle in enumerate(candles):
            body_size = abs(candle['close'] - candle['open'])
            total_range = candle['high'] - candle['low']

            if total_range == 0:
                weak_candles.append({'index': i, 'reason': 'no_range'})
                continue

            body_percentage = body_size / total_range

            # Body must be > 40% of total range
            if body_percentage >= 0.4:
                strong_candles.append({'index': i, 'strength': body_percentage})
            else:
                weak_candles.append({'index': i, 'reason': 'weak_body', 'strength': body_percentage})

        # Allow if at least 3 out of 4 candles are strong (75% success rate)
        min_required = max(1, int(len(candles) * 0.75))  # At least 75% must be strong
        is_strong_enough = len(strong_candles) >= min_required

        return is_strong_enough, {'strong': strong_candles, 'weak': weak_candles}

    async def apply_technical_filters(self, candles: List[Dict], direction: str, daily_data: Dict[str, pd.DataFrame], time_slot: str) -> Tuple[bool, Dict]:
        """Apply RSI and EMA trend filters with detailed results"""
        filter_results = {
            'rsi_passed': [],
            'rsi_failed': [],
            'trend_passed': [],
            'trend_failed': [],
            'overall_passed': False
        }

        try:
            rsi_pass_count = 0
            trend_pass_count = 0
            total_candles = len(candles)

            # For each candle, check technical conditions
            for candle in candles:
                day_key = candle['day']
                if day_key not in daily_data:
                    continue

                df = daily_data[day_key]
                candle_datetime = candle['datetime']

                # Get data up to this candle for indicator calculation
                historical_data = df[df['datetime'] <= candle_datetime].copy()

                if len(historical_data) < 50:  # Need enough data for indicators
                    filter_results['rsi_failed'].append(f"{day_key}: insufficient_data")
                    filter_results['trend_failed'].append(f"{day_key}: insufficient_data")
                    continue

                # Calculate technical indicators
                rsi = self.calculate_rsi(historical_data['close'], period=14)
                ema20 = self.calculate_ema(historical_data['close'], period=20)
                ema50 = self.calculate_ema(historical_data['close'], period=50)

                if len(rsi) == 0 or len(ema20) == 0 or len(ema50) == 0:
                    filter_results['rsi_failed'].append(f"{day_key}: calculation_error")
                    filter_results['trend_failed'].append(f"{day_key}: calculation_error")
                    continue

                current_rsi = rsi.iloc[-1]
                current_ema20 = ema20.iloc[-1]
                current_ema50 = ema50.iloc[-1]

                # Check RSI filter
                rsi_passed = False
                if direction == 'DOWN' and current_rsi < 40:
                    rsi_passed = True
                elif direction == 'UP' and current_rsi > 60:
                    rsi_passed = True

                if rsi_passed:
                    filter_results['rsi_passed'].append(f"{day_key}: RSI={current_rsi:.1f}")
                    rsi_pass_count += 1
                else:
                    filter_results['rsi_failed'].append(f"{day_key}: RSI={current_rsi:.1f}")

                # Check trend filter (EMA20 vs EMA50)
                trend_passed = False
                if direction == 'DOWN' and current_ema20 < current_ema50:
                    trend_passed = True
                elif direction == 'UP' and current_ema20 > current_ema50:
                    trend_passed = True

                if trend_passed:
                    filter_results['trend_passed'].append(f"{day_key}: EMA20={current_ema20:.5f}, EMA50={current_ema50:.5f}")
                    trend_pass_count += 1
                else:
                    filter_results['trend_failed'].append(f"{day_key}: EMA20={current_ema20:.5f}, EMA50={current_ema50:.5f}")

            # Allow signal if at least 50% pass each filter (relaxed requirements)
            min_required = max(1, int(total_candles * 0.5))
            rsi_sufficient = rsi_pass_count >= min_required
            trend_sufficient = trend_pass_count >= min_required

            filter_results['overall_passed'] = rsi_sufficient and trend_sufficient

            return filter_results['overall_passed'], filter_results

        except Exception as e:
            print_colored(f"Error in technical filters: {e}", "ERROR")
            filter_results['error'] = str(e)
            return False, filter_results

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))

            return rsi.fillna(50)  # Fill NaN with neutral value

        except Exception:
            return pd.Series([50] * len(prices), index=prices.index)

    def calculate_ema(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate Exponential Moving Average"""
        try:
            return prices.ewm(span=period, adjust=False).mean()
        except Exception:
            return pd.Series([prices.mean()] * len(prices), index=prices.index)

    def calculate_confidence(self, candles: List[Dict], direction: str) -> float:
        """Calculate confidence score for the signal"""
        try:
            total_score = 0
            max_score = 0

            for candle in candles:
                # Base score for consistent direction
                total_score += 60
                max_score += 100

                # Bonus for strong candle body
                body_size = abs(candle['close'] - candle['open'])
                total_range = candle['high'] - candle['low']

                if total_range > 0:
                    body_strength = body_size / total_range
                    if body_strength > 0.7:
                        total_score += 15
                    elif body_strength > 0.6:
                        total_score += 10

                # Bonus for volume (if available)
                if candle.get('volume', 0) > 0:
                    total_score += 5

            confidence = (total_score / max_score) * 100 if max_score > 0 else 0
            return min(confidence, 100)

        except Exception:
            return 50.0

    def display_results(self, inputs: Dict, signals: List[Dict], partial_matches: List[Dict] = None):
        """Display the final signal results including partial matches"""
        print_header("📅 ADVANCE SIGNAL ANALYSIS RESULTS")

        print_colored(f"📊 Analysis Summary:", "SKY_BLUE", bold=True)
        print_colored(f"   Trading Pair: {inputs['pair']} ({inputs['oanda_pair']})", "INFO")
        print_colored(f"   Timeframe: {inputs['timeframe']} ({inputs['timeframe_minutes']} minutes)", "INFO")
        print_colored(f"   Analysis Period: {inputs['days']} working days", "INFO")
        print_colored(f"   Time Range: {inputs['start_time']} to {inputs['end_time']}", "INFO")
        print()

        # Display qualifying signals
        if signals:
            print_colored(f"✅ Found {len(signals)} qualifying signals:", "SUCCESS", bold=True)
            print()

            # Sort signals by time
            signals.sort(key=lambda x: x['time'])

            # Display signals in a simple list first
            for signal in signals:
                direction_color = "SUCCESS" if signal['direction'] == 'UP' else "ERROR"
                direction_emoji = "📈" if signal['direction'] == 'UP' else "📉"

                print_colored(f"{signal['time']} {direction_emoji} {signal['direction']}", direction_color, bold=True)

            print()
            print_colored("📋 Signal Details:", "SKY_BLUE", bold=True)
            print_colored("=" * 80, "SKY_BLUE")

            # Header for the table
            header = f"{'TIME':<8} {'DIRECTION':<10} {'CONFIDENCE':<12} {'DAYS':<6} {'STATUS':<10}"
            print_colored(header, "TROPICAL_RAINFOREST", bold=True)
            print_colored("-" * 80, "SKY_BLUE")

            # Display each signal in table format
            for signal in signals:
                direction_color = "SUCCESS" if signal['direction'] == 'UP' else "ERROR"
                direction_symbol = "📈 UP" if signal['direction'] == 'UP' else "📉 DOWN"

                row = f"{signal['time']:<8} {direction_symbol:<10} {signal['confidence']:.1f}%{'':<7} {signal['candle_count']:<6} {'QUALIFIED':<10}"
                print_colored(row, direction_color)

            print_colored("=" * 80, "SKY_BLUE")
        else:
            print_colored("❌ No fully qualifying signals found", "ERROR", bold=True)

        # Display partial matches
        if partial_matches:
            print()
            print_colored(f"⚠️ Found {len(partial_matches)} partial matches (some filters failed):", "WARNING", bold=True)
            print()

            partial_matches.sort(key=lambda x: x['time'])

            for match in partial_matches:
                direction_color = "SUCCESS" if match['direction'] == 'UP' else "ERROR"
                direction_emoji = "📈" if match['direction'] == 'UP' else "📉"
                failed_reasons = ", ".join(match.get('failed_reasons', []))

                print_colored(f"{match['time']} {direction_emoji} {match['direction']} - Failed: {failed_reasons}", "WARNING")

                # Show detailed failure reasons
                if not match.get('strength_passed', True):
                    weak_count = len(match.get('strength_details', {}).get('weak', []))
                    print_colored(f"   └─ Candle Strength: {weak_count} weak candles", "WARNING")

                if not match.get('filters_passed', True):
                    filter_details = match.get('filter_details', {})
                    rsi_failed = len(filter_details.get('rsi_failed', []))
                    trend_failed = len(filter_details.get('trend_failed', []))
                    if rsi_failed > 0:
                        print_colored(f"   └─ RSI Filter: {rsi_failed} days failed", "WARNING")
                    if trend_failed > 0:
                        print_colored(f"   └─ Trend Filter: {trend_failed} days failed", "WARNING")

        if not signals and not partial_matches:
            print_colored("💡 Try adjusting your criteria:", "INFO")
            print_colored("   • Increase the number of analysis days", "INFO")
            print_colored("   • Expand the time range", "INFO")
            print_colored("   • Choose a different timeframe", "INFO")

        # Save results to file
        self.save_results_to_file(inputs, signals, partial_matches)

    def save_results_to_file(self, inputs: Dict, signals: List[Dict], partial_matches: List[Dict] = None):
        """Save results to CSV and text files"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Save qualifying signals as CSV
            if signals:
                # Create simplified version for CSV
                csv_signals = []
                for signal in signals:
                    csv_signals.append({
                        'time': signal['time'],
                        'direction': signal['direction'],
                        'candle_count': signal['candle_count'],
                        'confidence': signal['confidence']
                    })

                df_signals = pd.DataFrame(csv_signals)
                csv_path = os.path.join(self.signals_dir, f"signals_{timestamp}.csv")
                df_signals.to_csv(csv_path, index=False)
                print_colored(f"💾 Signals saved to: {csv_path}", "SUCCESS")

            # Save partial matches as CSV
            if partial_matches:
                csv_partial = []
                for match in partial_matches:
                    csv_partial.append({
                        'time': match['time'],
                        'direction': match['direction'],
                        'candle_count': match['candle_count'],
                        'confidence': match['confidence'],
                        'failed_reasons': ', '.join(match.get('failed_reasons', []))
                    })

                df_partial = pd.DataFrame(csv_partial)
                partial_csv_path = os.path.join(self.signals_dir, f"partial_matches_{timestamp}.csv")
                df_partial.to_csv(partial_csv_path, index=False)
                print_colored(f"💾 Partial matches saved to: {partial_csv_path}", "SUCCESS")

            # Save comprehensive text report
            txt_path = os.path.join(self.signals_dir, f"analysis_report_{timestamp}.txt")
            with open(txt_path, 'w') as f:
                f.write(f"Advance Signal Analysis Results\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(f"Trading Pair: {inputs['pair']} ({inputs['oanda_pair']})\n")
                f.write(f"Timeframe: {inputs['timeframe']} ({inputs['timeframe_minutes']} minutes)\n")
                f.write(f"Analysis Period: {inputs['days']} working days\n")
                f.write(f"Time Range: {inputs['start_time']} to {inputs['end_time']}\n\n")

                if signals:
                    f.write("QUALIFYING SIGNALS:\n")
                    f.write("=" * 50 + "\n")
                    for signal in signals:
                        f.write(f"{signal['time']} {signal['direction']} (Confidence: {signal['confidence']:.1f}%)\n")
                    f.write("\n")
                else:
                    f.write("No qualifying signals found.\n\n")

                if partial_matches:
                    f.write("PARTIAL MATCHES (Some filters failed):\n")
                    f.write("=" * 50 + "\n")
                    for match in partial_matches:
                        failed_reasons = ", ".join(match.get('failed_reasons', []))
                        f.write(f"{match['time']} {match['direction']} - Failed: {failed_reasons}\n")
                    f.write("\n")

            print_colored(f"📄 Comprehensive report saved to: {txt_path}", "SUCCESS")

        except Exception as e:
            print_colored(f"❌ Error saving results: {e}", "ERROR")


# Global instance
advance_signal_analyzer = AdvanceSignalAnalyzer()

# Main function for integration
async def run_advance_signal_analysis():
    """Main entry point for advance signal analysis"""
    await advance_signal_analyzer.run_analysis()
