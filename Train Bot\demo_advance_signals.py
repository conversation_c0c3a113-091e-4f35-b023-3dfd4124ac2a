#!/usr/bin/env python3
"""
Demo script for advance signal analyzer
Shows the complete functionality working with real data
"""

import asyncio
import sys
import os
from unittest.mock import patch

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advance_signal_analyzer import AdvanceSignalAnalyzer
from utils import print_colored, print_header

async def demo_advance_signals():
    """Demonstrate the advance signal analyzer with real analysis"""
    print_header("🔮 ADVANCE SIGNAL ANALYZER DEMO")
    print_colored("🎯 Demonstrating complete signal analysis functionality", "INFO", bold=True)
    print_colored("📊 This demo will analyze EUR/USD for repeating patterns", "INFO")
    print()
    
    analyzer = AdvanceSignalAnalyzer()
    
    # Demo inputs - realistic but limited for demonstration
    demo_inputs = [
        "EUR/USD",  # Popular trading pair
        "5",        # 5-minute timeframe
        "3",        # 3 working days (minimum for pattern detection)
        "14:00",    # Start time (London session)
        "16:00"     # End time (2-hour window)
    ]
    
    try:
        print_colored("🎬 Starting demonstration with predefined inputs:", "SKY_BLUE", bold=True)
        print_colored("   Trading Pair: EUR/USD", "INFO")
        print_colored("   Timeframe: 5 minutes", "INFO")
        print_colored("   Analysis Period: 3 working days", "INFO")
        print_colored("   Time Range: 14:00 to 16:00 (London session)", "INFO")
        print()
        
        with patch('builtins.input', side_effect=demo_inputs):
            # Step 1: Get inputs
            print_colored("📝 Step 1: Processing inputs...", "INFO")
            inputs = await analyzer.get_user_inputs()
            
            if not inputs:
                print_colored("❌ Failed to process inputs", "ERROR")
                return
            
            print_colored("✅ Inputs processed successfully", "SUCCESS")
            print()
            
            # Step 2: Calculate working days
            print_colored("📅 Step 2: Calculating working days...", "INFO")
            working_days = analyzer.calculate_working_days(inputs['days'])
            print_colored(f"✅ Found {len(working_days)} working days:", "SUCCESS")
            for i, day in enumerate(working_days, 1):
                print_colored(f"   Day {i}: {day.strftime('%Y-%m-%d (%A)')}", "INFO")
            print()
            
            # Step 3: Fetch data for all days
            print_colored("📊 Step 3: Fetching historical data...", "INFO")
            daily_data = await analyzer.fetch_daily_data(inputs, working_days)
            
            if not daily_data:
                print_colored("❌ Failed to fetch sufficient data", "ERROR")
                return
            
            print_colored(f"✅ Successfully fetched data for {len(daily_data)} days", "SUCCESS")
            for day_key, df in daily_data.items():
                print_colored(f"   {day_key}: {len(df)} candles", "INFO")
            print()
            
            # Step 4: Analyze time slots
            print_colored("🔍 Step 4: Analyzing time slots for patterns...", "INFO")
            signals = await analyzer.analyze_time_slots(inputs, daily_data)
            
            print_colored(f"✅ Analysis completed: {len(signals)} qualifying signals found", "SUCCESS")
            print()
            
            # Step 5: Display results
            print_colored("📋 Step 5: Displaying results...", "INFO")
            analyzer.display_results(inputs, signals)
            
            print()
            print_colored("🎉 DEMONSTRATION COMPLETED SUCCESSFULLY!", "SUCCESS", bold=True)
            print_colored("💡 The advance signal analyzer is fully functional and ready for use!", "INFO")
            print_colored("🚀 You can now use option 4 in the main bot menu to run your own analysis!", "SKY_BLUE")
            
    except Exception as e:
        print_colored(f"❌ Error during demonstration: {e}", "ERROR")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(demo_advance_signals())
