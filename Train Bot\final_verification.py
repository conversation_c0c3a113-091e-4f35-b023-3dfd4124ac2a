#!/usr/bin/env python3
"""
Final comprehensive verification of the advance signal analyzer
"""

import asyncio
import sys
import os
from unittest.mock import patch

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils import print_colored, print_header

async def final_verification():
    """Comprehensive final verification"""
    print_header("🎯 FINAL COMPREHENSIVE VERIFICATION")
    print_colored("🔮 Testing complete advance signal analyzer integration", "INFO", bold=True)
    print()
    
    verification_passed = True
    
    try:
        # Test 1: Import verification
        print_colored("📦 Test 1: Module imports...", "INFO")
        try:
            from advance_signal_analyzer import run_advance_signal_analysis, AdvanceSignalAnalyzer
            from Model import show_menu  # Test main bot integration
            print_colored("✅ All imports successful", "SUCCESS")
        except Exception as e:
            print_colored(f"❌ Import failed: {e}", "ERROR")
            verification_passed = False
        
        # Test 2: Menu integration
        print_colored("📋 Test 2: Menu integration...", "INFO")
        try:
            # Capture menu output to verify option 4 exists
            import io
            from contextlib import redirect_stdout
            
            f = io.StringIO()
            with redirect_stdout(f):
                show_menu()
            menu_output = f.getvalue()
            
            if "Advance Signal Analysis" in menu_output:
                print_colored("✅ Menu option 4 correctly integrated", "SUCCESS")
            else:
                print_colored("❌ Menu option 4 not found", "ERROR")
                verification_passed = False
                
        except Exception as e:
            print_colored(f"❌ Menu integration test failed: {e}", "ERROR")
            verification_passed = False
        
        # Test 3: OANDA connectivity
        print_colored("🌐 Test 3: OANDA API connectivity...", "INFO")
        try:
            from utils import get_oanda_headers
            from config import OANDA_CONFIG
            import requests
            
            headers = get_oanda_headers()
            url = f"{OANDA_CONFIG['BASE_URL']}/v3/accounts/{OANDA_CONFIG['ACCOUNT_ID']}"
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                print_colored("✅ OANDA API connection successful", "SUCCESS")
            else:
                print_colored(f"❌ OANDA API connection failed: {response.status_code}", "ERROR")
                verification_passed = False
                
        except Exception as e:
            print_colored(f"❌ OANDA connectivity test failed: {e}", "ERROR")
            verification_passed = False
        
        # Test 4: Core functionality
        print_colored("🔧 Test 4: Core analyzer functionality...", "INFO")
        try:
            analyzer = AdvanceSignalAnalyzer()
            
            # Test basic functions
            oanda_pair = analyzer.convert_to_oanda_format("EURUSD")
            time_valid = analyzer.validate_time_format("12:30")
            working_days = analyzer.calculate_working_days(3)
            time_slots = analyzer.generate_time_slots("12:00", "13:00", 5)
            
            if oanda_pair == "EUR_USD" and time_valid and len(working_days) == 3 and len(time_slots) > 0:
                print_colored("✅ Core functionality working correctly", "SUCCESS")
            else:
                print_colored("❌ Core functionality test failed", "ERROR")
                verification_passed = False
                
        except Exception as e:
            print_colored(f"❌ Core functionality test failed: {e}", "ERROR")
            verification_passed = False
        
        # Test 5: Data processing
        print_colored("📊 Test 5: Data processing capabilities...", "INFO")
        try:
            # Test with mock candle data
            mock_candles = [
                {'open': 1.1000, 'high': 1.1020, 'low': 1.0990, 'close': 1.1015, 'volume': 1000},
                {'open': 1.1015, 'high': 1.1030, 'low': 1.1005, 'close': 1.1025, 'volume': 1200}
            ]
            
            direction = analyzer.check_same_direction(mock_candles)
            strength = analyzer.check_candle_strength(mock_candles)
            confidence = analyzer.calculate_confidence(mock_candles, direction or 'UP')
            
            if direction and isinstance(strength, bool) and 0 <= confidence <= 100:
                print_colored("✅ Data processing working correctly", "SUCCESS")
            else:
                print_colored("❌ Data processing test failed", "ERROR")
                verification_passed = False
                
        except Exception as e:
            print_colored(f"❌ Data processing test failed: {e}", "ERROR")
            verification_passed = False
        
        # Test 6: File system operations
        print_colored("📁 Test 6: File system operations...", "INFO")
        try:
            if os.path.exists("advance_signals"):
                print_colored("✅ Signals directory exists", "SUCCESS")
            else:
                print_colored("⚠️ Signals directory will be created on first run", "WARNING")
            
            # Check if we can write to the directory
            test_file = os.path.join("advance_signals", "test.txt")
            with open(test_file, 'w') as f:
                f.write("test")
            
            if os.path.exists(test_file):
                os.remove(test_file)
                print_colored("✅ File system operations working", "SUCCESS")
            else:
                print_colored("❌ File system operations failed", "ERROR")
                verification_passed = False
                
        except Exception as e:
            print_colored(f"❌ File system test failed: {e}", "ERROR")
            verification_passed = False
        
        # Final result
        print()
        if verification_passed:
            print_colored("🎉 ALL VERIFICATION TESTS PASSED!", "SUCCESS", bold=True)
            print_colored("✅ The advance signal analyzer is fully functional and ready for production use!", "SUCCESS")
            print()
            print_colored("📋 Verification Summary:", "SKY_BLUE", bold=True)
            print_colored("   ✅ Module imports working", "SUCCESS")
            print_colored("   ✅ Menu integration complete", "SUCCESS")
            print_colored("   ✅ OANDA API connectivity confirmed", "SUCCESS")
            print_colored("   ✅ Core functionality operational", "SUCCESS")
            print_colored("   ✅ Data processing capabilities verified", "SUCCESS")
            print_colored("   ✅ File system operations working", "SUCCESS")
            print()
            print_colored("🚀 READY FOR USE:", "SKY_BLUE", bold=True)
            print_colored("   1. Run: python \"Train Bot/Model.py\"", "INFO")
            print_colored("   2. Enter auth key: miketester2390", "INFO")
            print_colored("   3. Select option 4: Advance Signal Analysis", "INFO")
            print_colored("   4. Follow the prompts to analyze your trading pairs!", "INFO")
            
        else:
            print_colored("❌ VERIFICATION FAILED!", "ERROR", bold=True)
            print_colored("Some tests did not pass. Please check the errors above.", "ERROR")
            
    except Exception as e:
        print_colored(f"❌ Verification process failed: {e}", "ERROR")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(final_verification())
