#!/usr/bin/env python3
"""
Simple test for advance signal analyzer
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("Starting simple test...")

try:
    from advance_signal_analyzer import AdvanceSignalAnalyzer
    print("✅ Import successful")
    
    analyzer = AdvanceSignalAnalyzer()
    print("✅ Analyzer created")
    
    # Test basic functions
    result = analyzer.convert_to_oanda_format("EURUSD")
    print(f"✅ OANDA format conversion: EURUSD -> {result}")
    
    result = analyzer.validate_time_format("12:30")
    print(f"✅ Time validation: 12:30 -> {result}")
    
    working_days = analyzer.calculate_working_days(3)
    print(f"✅ Working days calculation: {len(working_days)} days")
    
    print("✅ All basic tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("Test completed.")
