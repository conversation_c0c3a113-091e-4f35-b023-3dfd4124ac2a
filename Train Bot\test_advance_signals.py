#!/usr/bin/env python3
"""
Test script for advance signal analyzer
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advance_signal_analyzer import run_advance_signal_analysis
from utils import print_colored, print_header

async def test_advance_signals():
    """Test the advance signal analysis functionality"""
    print_header("🧪 TESTING ADVANCE SIGNAL ANALYZER")
    print_colored("🔮 Testing advance signal analysis functionality", "INFO", bold=True)
    print()

    try:
        print_colored("📝 Starting advance signal analysis...", "INFO")
        await run_advance_signal_analysis()
        print_colored("✅ Test completed successfully!", "SUCCESS")
    except Exception as e:
        print_colored(f"❌ Error during test: {e}", "ERROR")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_advance_signals())
