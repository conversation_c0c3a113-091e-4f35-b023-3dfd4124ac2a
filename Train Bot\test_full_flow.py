#!/usr/bin/env python3
"""
Test full advance signal analyzer flow
"""

import asyncio
import sys
import os
from unittest.mock import patch

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advance_signal_analyzer import AdvanceSignalAnalyzer
from utils import print_colored, print_header

async def test_full_flow():
    """Test the complete flow with limited data"""
    print_header("🧪 FULL FLOW TEST")
    print_colored("🔮 Testing complete advance signal analysis flow", "INFO", bold=True)
    print()
    
    analyzer = AdvanceSignalAnalyzer()
    
    # Use minimal inputs for faster testing
    mock_inputs = [
        "EUR/USD",  # Trading pair
        "5",        # Timeframe (5 minutes for faster testing)
        "3",        # Number of days (minimum)
        "13:00",    # Start time
        "13:30"     # End time (30 minutes only)
    ]
    
    try:
        with patch('builtins.input', side_effect=mock_inputs):
            print_colored("📝 Step 1: Getting user inputs...", "INFO")
            inputs = await analyzer.get_user_inputs()
            
            if not inputs:
                print_colored("❌ Failed to get inputs", "ERROR")
                return
            
            print_colored("✅ Step 1 completed: Inputs collected", "SUCCESS")
            
            print_colored("📅 Step 2: Calculating working days...", "INFO")
            working_days = analyzer.calculate_working_days(inputs['days'])
            print_colored(f"✅ Step 2 completed: {len(working_days)} working days", "SUCCESS")
            
            print_colored("⏰ Step 3: Generating time slots...", "INFO")
            time_slots = analyzer.generate_time_slots(inputs['start_time'], inputs['end_time'], inputs['timeframe_minutes'])
            print_colored(f"✅ Step 3 completed: {len(time_slots)} time slots", "SUCCESS")
            
            print_colored("📊 Step 4: Testing data fetch for one day...", "INFO")
            try:
                # Test fetching data for just one day
                test_day = working_days[0]
                df = await analyzer.fetch_day_data(inputs, test_day)
                
                if df is not None and len(df) > 0:
                    print_colored(f"✅ Step 4 completed: Fetched {len(df)} candles for {test_day.strftime('%Y-%m-%d')}", "SUCCESS")
                else:
                    print_colored("⚠️ Step 4: No data available for test day", "WARNING")
                    
            except Exception as e:
                print_colored(f"⚠️ Step 4: Data fetch error - {e}", "WARNING")
            
            print_colored("🔍 Step 5: Testing candle analysis functions...", "INFO")
            
            # Test candle strength check with mock data
            mock_candles = [
                {
                    'open': 1.1000,
                    'high': 1.1020,
                    'low': 1.0990,
                    'close': 1.1015,
                    'volume': 1000
                },
                {
                    'open': 1.1015,
                    'high': 1.1030,
                    'low': 1.1005,
                    'close': 1.1025,
                    'volume': 1200
                }
            ]
            
            direction = analyzer.check_same_direction(mock_candles)
            strength_ok = analyzer.check_candle_strength(mock_candles)
            confidence = analyzer.calculate_confidence(mock_candles, direction or 'UP')
            
            print_colored(f"✅ Step 5 completed:", "SUCCESS")
            print_colored(f"   Direction: {direction}", "INFO")
            print_colored(f"   Strength OK: {strength_ok}", "INFO")
            print_colored(f"   Confidence: {confidence:.1f}%", "INFO")
            
            print_colored("🎉 All tests completed successfully!", "SUCCESS", bold=True)
            print_colored("💡 The advance signal analyzer is ready for use!", "INFO")
            
    except Exception as e:
        print_colored(f"❌ Error during test: {e}", "ERROR")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_full_flow())
