#!/usr/bin/env python3
"""
Test OANDA API connection
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils import get_oanda_headers, print_colored
from config import OANDA_CONFIG
import requests

def test_oanda_connection():
    """Test OANDA API connection"""
    print_colored("🧪 Testing OANDA API Connection...", "INFO", bold=True)
    
    try:
        headers = get_oanda_headers()
        url = f"{OANDA_CONFIG['BASE_URL']}/v3/accounts/{OANDA_CONFIG['ACCOUNT_ID']}"
        
        print_colored(f"🔗 Connecting to: {url}", "INFO")
        response = requests.get(url, headers=headers, timeout=10)
        
        print_colored(f"📊 Status Code: {response.status_code}", "INFO")
        
        if response.status_code == 200:
            print_colored("✅ OANDA connection successful!", "SUCCESS")
            account_data = response.json()
            account_info = account_data.get('account', {})
            print_colored(f"   Account ID: {account_info.get('id', 'Unknown')}", "INFO")
            print_colored(f"   Currency: {account_info.get('currency', 'Unknown')}", "INFO")
            print_colored(f"   Balance: {account_info.get('balance', 'Unknown')}", "INFO")
            return True
        else:
            print_colored(f"❌ OANDA connection failed: {response.status_code}", "ERROR")
            print_colored(f"Response: {response.text}", "ERROR")
            return False
            
    except Exception as e:
        print_colored(f"❌ Error testing OANDA connection: {e}", "ERROR")
        return False

if __name__ == "__main__":
    test_oanda_connection()
