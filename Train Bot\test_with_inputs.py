#!/usr/bin/env python3
"""
Test advance signal analyzer with simulated inputs
"""

import asyncio
import sys
import os
from unittest.mock import patch

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advance_signal_analyzer import AdvanceSignalAnalyzer
from utils import print_colored, print_header

async def test_with_simulated_inputs():
    """Test with simulated user inputs"""
    print_header("🧪 TESTING WITH SIMULATED INPUTS")
    print_colored("🔮 Testing advance signal analysis with mock inputs", "INFO", bold=True)
    print()
    
    analyzer = AdvanceSignalAnalyzer()
    
    # Simulate user inputs
    mock_inputs = [
        "EUR/USD",  # Trading pair
        "1",        # Timeframe (1 minute)
        "3",        # Number of days
        "12:00",    # Start time
        "14:00"     # End time
    ]
    
    try:
        with patch('builtins.input', side_effect=mock_inputs):
            print_colored("📝 Getting user inputs with mock data...", "INFO")
            inputs = await analyzer.get_user_inputs()
            
            if inputs:
                print_colored("✅ User inputs collected successfully!", "SUCCESS")
                print_colored(f"   Pair: {inputs['pair']} -> {inputs['oanda_pair']}", "INFO")
                print_colored(f"   Timeframe: {inputs['timeframe']} ({inputs['timeframe_minutes']} min)", "INFO")
                print_colored(f"   Days: {inputs['days']}", "INFO")
                print_colored(f"   Time Range: {inputs['start_time']} to {inputs['end_time']}", "INFO")
                
                # Test working days calculation
                working_days = analyzer.calculate_working_days(inputs['days'])
                print_colored(f"✅ Working days: {len(working_days)} days calculated", "SUCCESS")
                
                # Test time slot generation
                time_slots = analyzer.generate_time_slots(inputs['start_time'], inputs['end_time'], inputs['timeframe_minutes'])
                print_colored(f"✅ Time slots: {len(time_slots)} slots generated", "SUCCESS")
                print_colored(f"   First few slots: {time_slots[:5]}", "INFO")
                
                print_colored("✅ All input processing tests passed!", "SUCCESS")
            else:
                print_colored("❌ Failed to get user inputs", "ERROR")
                
    except Exception as e:
        print_colored(f"❌ Error during test: {e}", "ERROR")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_with_simulated_inputs())
