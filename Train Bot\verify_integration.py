#!/usr/bin/env python3
"""
Verify that the advance signal analyzer is properly integrated
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils import print_colored, print_header

def verify_integration():
    """Verify all components are properly integrated"""
    print_header("🔧 INTEGRATION VERIFICATION")
    print_colored("🔍 Verifying advance signal analyzer integration", "INFO", bold=True)
    print()
    
    try:
        # Test 1: Import advance signal analyzer
        print_colored("📦 Test 1: Importing advance signal analyzer...", "INFO")
        from advance_signal_analyzer import run_advance_signal_analysis, AdvanceSignalAnalyzer
        print_colored("✅ Import successful", "SUCCESS")
        
        # Test 2: Check if function is available
        print_colored("🔧 Test 2: Checking function availability...", "INFO")
        if callable(run_advance_signal_analysis):
            print_colored("✅ run_advance_signal_analysis function is callable", "SUCCESS")
        else:
            print_colored("❌ Function is not callable", "ERROR")
            return False
        
        # Test 3: Check analyzer class
        print_colored("🏗️ Test 3: Testing analyzer class instantiation...", "INFO")
        analyzer = AdvanceSignalAnalyzer()
        print_colored("✅ AdvanceSignalAnalyzer class instantiated successfully", "SUCCESS")
        
        # Test 4: Check required directories
        print_colored("📁 Test 4: Checking directory structure...", "INFO")
        if os.path.exists("advance_signals"):
            print_colored("✅ advance_signals directory exists", "SUCCESS")
        else:
            print_colored("⚠️ advance_signals directory not found (will be created on first run)", "WARNING")
        
        # Test 5: Check OANDA integration
        print_colored("🌐 Test 5: Testing OANDA integration...", "INFO")
        from utils import get_oanda_headers
        from config import OANDA_CONFIG
        headers = get_oanda_headers()
        if headers and 'Authorization' in headers:
            print_colored("✅ OANDA API integration available", "SUCCESS")
        else:
            print_colored("❌ OANDA API integration failed", "ERROR")
            return False
        
        # Test 6: Check dependencies
        print_colored("📚 Test 6: Checking dependencies...", "INFO")
        try:
            import holidays
            import pandas as pd
            import numpy as np
            print_colored("✅ All required dependencies available", "SUCCESS")
        except ImportError as e:
            print_colored(f"❌ Missing dependency: {e}", "ERROR")
            return False
        
        print()
        print_colored("🎉 ALL INTEGRATION TESTS PASSED!", "SUCCESS", bold=True)
        print_colored("✅ The advance signal analyzer is fully integrated and ready to use", "SUCCESS")
        print()
        print_colored("📋 Integration Summary:", "SKY_BLUE", bold=True)
        print_colored("   ✅ Module imports working", "SUCCESS")
        print_colored("   ✅ Function callable", "SUCCESS")
        print_colored("   ✅ Class instantiation working", "SUCCESS")
        print_colored("   ✅ Directory structure ready", "SUCCESS")
        print_colored("   ✅ OANDA API integration active", "SUCCESS")
        print_colored("   ✅ All dependencies available", "SUCCESS")
        print()
        print_colored("🚀 You can now use option 4 in the main bot menu!", "SKY_BLUE", bold=True)
        
        return True
        
    except Exception as e:
        print_colored(f"❌ Integration verification failed: {e}", "ERROR")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_integration()
